langtrace:
  langtraceApp:
    name: langtrace
    image: scale3labs/langtrace-client
    langtrace_release: latest
    imagePullPolicy: IfNotPresent
    containerPort: 3000
    replicaCount: 1
    restartPolicy: OnFailure
    maxRetry: 5
    serviceType: ClusterIP
    ingress:
      create: false

  postgres:
    enabled: true
    name: langtrace-postgres
    image: postgres:16.2-bookworm
    imagePullPolicy: IfNotPresent
    containerPort: 5432
    storageSize: 10Gi

  clickhouse:
    enabled: true
    name: langtrace-clickhouse
    image: clickhouse/clickhouse-server:24.5.1.1763-alpine
    imagePullPolicy: IfNotPresent
    containerPorts:
      - 8123
      - 9000
    storageSize: 10Gi

langtrace-common: {}

istio-ingress: {}
