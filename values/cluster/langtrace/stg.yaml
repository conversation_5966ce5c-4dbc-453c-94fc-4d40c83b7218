# Langtrace configuration for STAGING environment
langtrace:
  # Disable embedded databases - use external ones instead
  postgres:
    enabled: false

  clickhouse:
    enabled: false

  env:
    # External PostgreSQL Variables (using your existing infrastructure)
    postgres_host: "pg-langtrace-main.external-service.svc.cluster.local:5432"
    postgres_user: "langtrace"
    postgres_password: "$(POSTGRES_LANGTRACE_OWNER_PASSWORD)"
    postgres_database: "langtrace"

    # Application Variables
    NEXT_PUBLIC_APP_NAME: "Langtrace - Staging"
    NEXT_PUBLIC_ENVIRONMENT: "staging"
    NEXT_PUBLIC_HOST: "https://langtrace.stg.inspectorio.com"
    NEXTAUTH_SECRET: "difficultsecret-staging-change-me"

    # External ClickHouse Variables (using your existing clickhouse-ds cluster)
    CLICK_HOUSE_HOST: "http://clickhouse-ds-clickhouse-main.clickhouse-ds.svc.cluster.local:8123"
    CLICK_HOUSE_USER: "langtrace"
    CLICK_HOUSE_PASSWORD: "$(CLICKHOUSE_LANGTRACE_PASSWORD)"
    CLICK_HOUSE_DATABASE_NAME: "langtrace_traces"
    CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"

    # Admin credentials - should be moved to secrets
    ADMIN_EMAIL: "<EMAIL>"
    ADMIN_PASSWORD: "admin-password-change-me"
    NEXT_PUBLIC_ENABLE_ADMIN_LOGIN: "true"

    # Azure AD Variables (configure as needed)
    AZURE_AD_CLIENT_ID: ""
    AZURE_AD_CLIENT_SECRET: ""
    AZURE_AD_TENANT_ID: ""

langtrace-common:
  enabled: true

  # External Secret Store for database credentials
  externalSecretStore:
    langtrace-db-secrets:
      generateSecretName: true
      source:
        data:
          - secretKey: POSTGRES_LANGTRACE_OWNER_PASSWORD
            remoteRef:
              key: pg-langtrace-main
          - secretKey: CLICKHOUSE_LANGTRACE_PASSWORD
            remoteRef:
              key: clickhouse-ds-clickhouse-langtrace
  
  serviceAccount:
    create: true
    name: langtrace
    enableWorkloadIdentity: true

istio-ingress:
  enabled: true
  istio:
    ingress:
      langtrace:
        enabled: true
        gateways:
          - common
        hosts:
          - "langtrace.stg.inspectorio.com"
        http:
          - route:
              - destination:
                  host: langtrace-svc.langtrace.svc.cluster.local
                  port:
                    number: 3000
