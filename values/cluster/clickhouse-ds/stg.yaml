clickhouse-ds:
  infra:
    services:
      clickhousecluster:
        main:
          clickhouse:
            resources:
              requests:
                memory: "6Gi"
                cpu: "1"
              limits:
                memory: "8Gi"
                cpu: "4"
            databases:
              - bi_analytic
              - dbt_bi_analytic
              - target_analytic
              - walmart_analytic
              - cdc_sight_be_raw
              - cdc_sight_be
              - langtrace_traces
            profiles:
              default/allow_experimental_object_type: 1
            users:
              readonly:
                grafana: {}
                analytic3:
                  allowedDatabases:
                    - "bi_analytic"
                superset:
                  allowedDatabases:
                    - "bi_analytic"
                superset_target:
                  allowedDatabases:
                    - "target_analytic"
                superset_walmart:
                  allowedDatabases:
                    - "walmart_analytic"
              readwrite:
                airflow:
                  settings:
                    named_collection_control: 1
                developer:
                  settings:
                    named_collection_control: 1
                connector:
                  allowedDatabases:
                    - "cdc_sight_be"
                langtrace:
                  allowedDatabases:
                    - "langtrace_traces"
            postgresqlSource:
              datalake-main:
                password: DATALAKE_PASSWORD
            env:
              - name: DATALAKE_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: pg-datalake-main-clickhouse-ds
                    key: DB_PASSWORD

            backups:
              enabled: true
              retentionCount: "126"
              incrementalInterval: "3h"
              env:
                - name: CLICKHOUSE_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: clickhouse-ds-clickhouse-main
                      key: ch-chbackup-password
            persistency:
              storage: 30Gi

          clickhousekeeper:
            replicas: 1
            resources:
              requests:
                memory: "256Mi"
                cpu: "0.5"
              limits:
                memory: "2Gi"
                cpu: "1"

  serviceAccount:
    create: true
    name: clickhouse-ds
    enableWorkloadIdentity: true

  externalSecretStore:
    clickhouse-main:
      generateSecretName: true
      source:
        data:
          - secretKey: ch-default-password
            remoteRef:
              key: clickhouse-ds-clickhouse-default
          - secretKey: ch-chowner-password
            remoteRef:
              key: clickhouse-ds-clickhouse-chowner
          - secretKey: ch-chbackup-password
            remoteRef:
              key: clickhouse-ds-clickhouse-chbackup
          - secretKey: ch-analytic3-password
            remoteRef:
              key: clickhouse-ds-clickhouse-analytic3
          - secretKey: ch-superset-password
            remoteRef:
              key: clickhouse-ds-clickhouse-superset
          - secretKey: ch-superset_target-password
            remoteRef:
              key: clickhouse-ds-clickhouse-superset_target
          - secretKey: ch-superset_walmart-password
            remoteRef:
              key: clickhouse-ds-clickhouse-superset_walmart
          - secretKey: ch-airflow-password
            remoteRef:
              key: clickhouse-ds-clickhouse-airflow
          - secretKey: ch-langtrace-password
            remoteRef:
              key: clickhouse-ds-clickhouse-langtrace
          - secretKey: ch-developer-password
            remoteRef:
              key: clickhouse-ds-clickhouse-developer
          - secretKey: ch-grafana-password
            remoteRef:
              key: clickhouse-ds-clickhouse-grafana
          - secretKey: ch-connector-password
            remoteRef:
              key: clickhouse-ds-clickhouse-connector
    pg-datalake-main-clickhouse-ds: # name of k8s secret
      source:
        data:
          - secretKey: DB_PASSWORD # name of key in k8s secret
            remoteRef:
              key: pg-datalake-main-clickhouse-ds

  nodeSelector:
    data-platform: "true"
  tolerations:
    - key: "pool-type"
      operator: "Equal"
      value: "data-platform"
      effect: "NoSchedule"
