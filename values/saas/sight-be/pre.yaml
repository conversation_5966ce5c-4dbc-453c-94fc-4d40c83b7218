sight-be:
  appservices:
    main:
      enabled: true
      replicaCount: 3
      terminationGracePeriodSeconds: 240
      podAnnotations:
        proxy.istio.io/config: |
          terminationDrainDuration: 240s
      containers:
        main:
          resources:
            limits:
              memory: 5Gi
              cpu: 4
            requests:
              memory: 2Gi
              cpu: 400m
          readinessProbe:
            httpGet:
              path: /health_check
              port: main
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
          lifecycle:
            preStop:
              exec:
                command:
                  - bash
                  - '-c'
                  - sleep 30
      istio:
        wasmPlugins:
          api-key:
            pluginUrl: oci://us-central1-docker.pkg.dev/inspectorio-ant/wasm-istio-poc/gateway-apikey:0.1.19

      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='sight-be-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='sight-be-appservice-main'})"
          interval: 10s
          name: sight_be_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 8
        scalers:
          main:
            enabled: true
            minReplicaCount: 6
            maxReplicaCount: 10
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 70
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 200
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: "70"
    integration-api:
      enabled: true
      replicaCount: 3
      terminationGracePeriodSeconds: 240
      podAnnotations:
        proxy.istio.io/config: |
          terminationDrainDuration: 240s
      service:
        type: ClusterIP
        port: 80
        targetPort: main
        enabled: true
        protocol: TCP
      containers:
        main:
          environment:
            UWSGI_HTTP_TIMEOUT: 120
          ports:
            - name: main
              containerPort: 5000
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health_check
              port: main
            initialDelaySeconds: 5
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /health_check
              port: main
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
          resources:
            limits:
              memory: 5Gi
              cpu: 4
            requests:
              memory: 2Gi
              cpu: 0.5
          lifecycle:
            preStop:
              exec:
                command:
                  - bash
                  - '-c'
                  - sleep 30
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='sight-be-appservice-integration-api'}) / sum(kube_deployment_status_replicas{deployment='sight-be-appservice-integration-api'})"
          interval: 5s
          name: sight_be_integration_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 7
        scalers:
          integration-api:
            enabled: true
            minReplicaCount: 6
            maxReplicaCount: 12
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 60
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 180
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 240
                    selectPolicy: Max
            triggers:
              cpu:
                value: "70"
    metadata-inspection-consumer:
      enabled: true
    celery-beat:
      enabled: true
  workers:
    automation:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'automation', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-automation.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-automation.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-automation.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s # call expr each internval
          name: automation_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          automation:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 2Gi
              cpu: 600
            requests:
              memory: 1Gi
              cpu: 100m
    datasource:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'datasource', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-datasource.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-datasource.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-datasource.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s # call expr each internval
          name: datasource_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          datasource:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 1Gi
              cpu: 1
            requests:
              memory: 512Mi
              cpu: 200m
    inspection:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'inspection', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-inspection.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-inspection.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-inspection.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s
          name: inspection_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          inspection:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 2Gi
              cpu: 500m
            requests:
              memory: 1Gi
              cpu: 100m
    items:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'items', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-items.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-items.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-items.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s
          name: items_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          items:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 1Gi
              cpu: 500m
            requests:
              memory: 400Mi
              cpu: 100m
    notification:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'notification', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-notification.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-notification.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-notification.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s
          name: notification_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          notification:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 2Gi
              cpu: 1
            requests:
              memory: 1Gi
              cpu: 100m
    behavioraltracking:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'behavioraltracking', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-behavioraltracking.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-behavioraltracking.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-behavioraltracking.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s
          name: behavioraltracking_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
          query: behavioraltracking_workers_hpa_metric{namespace="sight-be"}
        scalers:
          behavioraltracking:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 1500Mi
              cpu: 1
            requests:
              memory: 768Mi
              cpu: 400m
    rulebuilder:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'rulebuilder', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-rulebuilder.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-rulebuilder.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-rulebuilder.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s
          name: rulebuilder_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          rulebuilder:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 1Gi
              cpu: 700m
            requests:
              memory: 400Mi
              cpu: 100m
    tracking:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'tracking', exported_namespace='sight-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-tracking.*', exported_namespace='sight-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-tracking.*',le='+Inf', exported_namespace='sight-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-tracking.*',le='2.5', exported_namespace='sight-be'}[1m])))or vector(1)))"
          interval: 10s
          name: tracking_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
          query: tracking_workers_hpa_metric{namespace="sight-be"}
        scalers:
          tracking:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      containers:
        main:
          resources:
            limits:
              memory: 1Gi
              cpu: 500m
            requests:
              memory: 400Mi
              cpu: 100m
    labsync:
      enabled: true
      containers:
        default:
          resources:
            limits:
              memory: 1.5Gi
              cpu: 1
            requests:
              memory: 768Mi
              cpu: 0.2
    labtestocr:
      enabled: true
      containers:
        default:
          resources:
            requests:
              memory: 0.8Gi
              cpu: 200m
            limits:
              memory: 2Gi
              cpu: 1
    celery:
      enabled: true
      containers:
        default:
          resources:
            requests:
              memory: 896Mi
              cpu: 350m
            limits:
              memory: 3Gi
              cpu: 1
    maintenance:
      enabled: true
  environment:
    PRODUCT_UPSERT_BATCH_SIZE: 50
    INSPECTION_PUBSUB_TOPIC: sight_inspection_pre
    DATABASE_URL: "postgis://sight-be:$(POSTGRES_OWNER_PASSWORD)@pgbouncer-sight-be-main.pgbouncer.svc.cluster.local:5432/sight-be?application_name=$(APP_NAME)-$(ROLE)-$(COMPONENT)"
    REPLICA_DATABASE_URL: "postgis://sight-be:$(POSTGRES_OWNER_PASSWORD)@pgbouncer-sight-be-main-replica.pgbouncer.svc.cluster.local:5432/sight-be?application_name=$(APP_NAME)-$(ROLE)-$(COMPONENT)"
    USE_REPLICA: "true"
    SMS_DATABASE_URL: "***************************************************************************************************************/passport-be?sslmode=require&sslcert=/tmp/dummycertpath"
    INTERNAL_IPS: "127.0.0.1,************,*************,**************,*************,*************"
    SMS_BASE_URL: http://auth-router.default.svc.cluster.local/sms/api/sms
    NOTIMANAGER_BASE_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1
    CAPA_BASE_URL: http://car-main.car.svc.cluster.local/v1
    ANALYTIC3_BASE_URL: http://analytic3-main.analytic3.svc.cluster.local
    AMS_BASE_URL: http://assignments-gcp-pre-assignments.default.svc.cluster.local/assignments
    WEB_APP_URL: https://app.pre.inspectorio.com
    DEFECT_PREDICTION_BASE_URL: http://defect-recommend-be-main.defect-recommend-be.svc.cluster.local
    IMS_BASE_URL: http://inspections-gcp-pre-inspections.default.svc.cluster.local/inspections
    REPORT_DETAIL_URL: https://app.pre.inspectorio.com/inspections/report-html-internal
    FMS_BASE_URL: http://fms-main.fms.svc.cluster.local
    REPORT_HTML_BASE_URL: http://report-html-main.report-html.svc.cluster.local
    HERMES_BASE_URL: http://hermes-be-main.hermes.svc.cluster.local
    TRANSLATION_BASE_URL: http://translation-main.translation.svc.cluster.local
    MASTER_DATA_BASE_URL: http://master-data-main.master-data.svc.cluster.local
    PRODUCT_RISK_BASE_URL: http://product-risk-be-main.product-risk.svc.cluster.local
    FACTORY_RISK_BASE_URL: http://factory-risk-be-main.factory-risk-be.svc.cluster.local
    DOCUMENT_VALIDATOR_BASE_URL: "http://document-validator-main.document-validator.svc.cluster.local"
    PASSPORT_BASE_URL: http://passport-be-main.passport.svc.cluster.local
    USERPILOT_BASE_URL: https://analytex-us.userpilot.io
    ELASTICSEARCH_HOST: http://eck-sight-monitoring-es-http.eck-sight.svc.cluster.local:9200
    ES_DATASYNC_HOST: http://eck-datasync-pg-es-http.eck-datasync.svc.cluster.local:9200
    ES_DATASYNC_USER: sight-be
    AUTO_SHARE_PO_ORGS: "309000,309001,309002"
    UNAVAILABLE_PLACEHOLDER_ORG: "305198"
    CACHE_URL: "redis://redis-sight-caches.external-service.svc.cluster.local:6379/3"
    CELERY_BROKER_URL: "redis://redis-sight-queues.external-service.svc.cluster.local:6379/3"
    INTEGRATION_RULE_BUILDER_KAFKA_BOOTSTRAP_SERVERS: kafka-pre-headless.default.svc.cluster.local
    INSPECTION_NS_KAFKA_URL: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092
    INSPECTION_NS_TOPIC: "master-data.org_style_topic"
    INTEGRATION_API_LOG_TOPIC: "sight-be.integration-logs"
    ASSETS_CATEGORY_UPDATE_TOPIC: "sight-be.assets-category-updates"
    ASSETS_CATEGORY_UPDATE_DLQ_TOPIC: "sight-be.assets-category-updates-dead-letter-queue"
    INTEGRATION_API_LOG_KAFKA_URL: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092
    SMS_USERNAME: <EMAIL>
    WORKER_PROCESSES: "1"
    WORKER_CONCURRENCY: "2"
    WORKER_AUTOSCALE: "10"
    CELERY_LOG_LEVEL: "INFO"
    UWSGI_PROCESSES: "6"
    SEQUENCE_ENCRYPTION_PUBLIC_KEY: "**********,127"
    RANDOMIZE_RECOMMENDED_INSPECTION_TYPE_CACHE_TTL: 604800
    LAB_TEST_FILTER_THRESHOLD: 10000
    DATA_PROVIDER_FOR_ITEM_FEATURE_ID: 1060
    LAB_TEST_OCR_JOB_POLLING_INTERVAL: 10
    LAB_TEST_OCR_JOB_POLLING_TIMEOUT: 1800

  environmentFrom:
    - secretRef:
        name: lst-sight-be

  environmentFromSecrets:
    ES_DATASYNC_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: eck-datasync-pg-sight-be-user
          key: password

  externalSecretStore:
    lst-sight-be: # name of k8s secret
      source:
        data:
          - secretKey: POSTGRES_OWNER_PASSWORD
            remoteRef:
              key: pg-sight-be-main
              version: latest
          - secretKey: POSTGRES_PASSPORT_PASSWORD
            remoteRef:
              key: pg-passport-be-main-sight-be
              version: latest
          - secretKey: POSTGRES_CDC_PASSWORD
            remoteRef:
              key: pg-sight-be-main-cdc
              version: latest
          - secretKey: SENTRY_DSN
            remoteRef:
              key: sight-be-sentry-default
              version: latest
          - secretKey: DATA_SYNC_SECRET
            remoteRef:
              key: sight-be-data-sync-secret
              version: latest
          - secretKey: DEBUG_SECRET
            remoteRef:
              key: sight-be-debug-secret
              version: latest
          - secretKey: GOOGLE_MAPS_API_KEY
            remoteRef:
              key: sight-be-google-maps-api-key
              version: latest
          - secretKey: SECRET_KEY
            remoteRef:
              key: passport-be-sms-jwt-secret-key
              version: latest
          - secretKey: JWT_SECRET_KEY
            remoteRef:
              key: passport-be-sms-jwt-secret-key
              version: latest
          - secretKey: SMS_PASSWORD
            remoteRef:
              key: sight-be-sms-password
              version: latest
          - secretKey: FMS_API_KEY
            remoteRef:
              key: sight-be-fms-api-key
              version: latest
          - secretKey: MIXPANEL_PROJECT_TOKEN
            remoteRef:
              key: sight-be-mixpanel-token
              version: latest
          - secretKey: USERPILOT_API_KEY
            remoteRef:
              key: sight-be-userpilot-api-key
              version: latest
          - secretKey: HERMES_AUTHENTICATION_KEY
            remoteRef:
              key: sight-be-hermes-authentication-key
              version: latest
          - secretKey: INTEGRATION_GATEWAY_ADMIN_KEY
            remoteRef:
              key: sight-be-hermes-authentication-key
              version: latest
          - secretKey: SEQUENCE_ENCRYPTION_PRIVATE_KEY
            remoteRef:
              key: sight-be-sequence-encryption-private-key
              version: latest
          - secretKey: DOCUMENT_VALIDATOR_API_KEY
            remoteRef:
              key: sight-be-document-validator-api-key
              version: latest
          - secretKey: PASSPORT_API_KEY
            remoteRef:
              key: subscription-be-passport-api-key
              version: latest

    eck-datasync-pg-connector-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-connector
              conversionStrategy: Default
              decodingStrategy: None

    eck-datasync-pg-sight-be-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-sight-be
              conversionStrategy: Default
              decodingStrategy: None


  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    # Below setting belong to KongGateway IngressAPI
    # Support KongGateway Migration
    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: false
      paths:
        - "/customer-data/"
        - "/purchase-orders/"
        - "/inspectorio/assets"
        - "/inspectorio/metrics/"
        - "/inspectorio/mobile/"
        - "/inspectorio/aql/"
        - "/inspectorio/ams/"
        - "/inspectorio/dss"
        - "/inspectorio/accounts/"
        - "/~/inspectorio/v1/[0-9]+/"
        - "/~/inspectorio/[0-9]+/"
        - "/inspectorio/v2/"
        - "/inspectorio/v3/"
        - "/inspectorio/bms/"
        - "/inspectorio/ims/"
        - "/inspectorio/monitoring/v1"
        - "/inspectorio/chatbot"
        - "/inspectorio/lab"
        - "/~/inspectorio/dss/v1/(.*)/lab"
        - "/~/inspectorio/integration/v1/api-keys(.*)"
        - "/inspectorio/boms"

    metrics:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
        strip_path: false
      paths:
        - "/inspectorio/metrics/"

    mobile:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: false
      paths:
        - "/inspectorio/mobile/"

    mobile-pos:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/(?P<app_version>(4\.(2[3-9]|[3-9]\d)(\.\d{1,})?|[5-9](\.\d{1,}){1,2}|\d{2,}(\.\d{1,}){1,2}))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: false
      paths:
        - "/~/inspectorio/mobile/pos/*"
        - "/inspectorio/mobile/pos"

    aql:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: false
      paths:
        - "/inspectorio/aql/"
    ams:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
        strip_path: false
      paths:
        - "/inspectorio/ams/"
    dss:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
        strip_path: false
        regex_priority: 300
      paths:
        - "/inspectorio/dss"
    accounts:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
      paths:
        - "/inspectorio/accounts/"
    assets:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
          - HEAD
        strip_path: false
      paths:
        - "/inspectorio/assets"
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        regex_priority: 300
      paths:
        - "/~/inspectorio/v1/[0-9]+/"
        - "/~/inspectorio/[0-9]+/"
        - "/inspectorio/v2/"
        - "/inspectorio/v3/"
        - "/inspectorio/lab"

    bms-api:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
        strip_path: false
      paths:
        - "/inspectorio/bms/"
    ims-api:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
        strip_path: false
      paths:
        - "/inspectorio/ims/"
    monitoring-v1-api:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
        strip_path: false
      paths:
        - "/inspectorio/monitoring/v1"

    chatbot:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
      paths:
        - "/inspectorio/chatbot"

    apikeys:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - DELETE
          - PATCH
          - GET
          - POST
        strip_path: false
      rewrite:
        uri: /integration/v1/api-keys$(uri_captures[1])
      paths:
        - /~/inspectorio/integration/v1/api-keys(.*)

    int-assets:
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
        limit-org:
          enabled: true
          plugin: rate-limiting
          config:
            limit_by: header
            header_name: x-apikey-orgid
            minute: 30
            policy: redis
            redis_host: redis-infra-shared-caches.external-service.svc.cluster.local
            redis_database: 2
      route:
        methods:
          - PUT
          - GET
          - POST
          - DELETE
          - PATCH
        strip_path: false
      rewrite:
        uri: /customer-data/v1$(uri_captures[1]) # Sight-be internal URL
      paths:
        - /~/qrm/v1/customer-data(.*)

    int-assets-legacy:
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
        limit-org:
          enabled: true
          plugin: rate-limiting
          config:
            limit_by: header
            header_name: x-apikey-orgid
            minute: 30
            policy: redis
            redis_host: redis-infra-shared-caches.external-service.svc.cluster.local
            redis_database: 2
      route:
        methods:
          - PUT
          - GET
          - POST
          - DELETE
          - PATCH
        strip_path: false
      rewrite:
        uri: /customer-data/v1$(uri_captures[1]) # Sight-be internal URL
      paths:
        - /~/api/v1/customer-data(.*)
      hosts:
        - domains:
            - sight.pre.inspectorio.com
            - integration-api.pre.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

    int-pos:
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
      route:
        methods:
          - POST
          - PUT
          - GET
          - DELETE
        strip_path: false
      rewrite:
        uri: /purchase-orders/v3$(uri_captures[1]) # Sight-be internal URL
      paths:
        - /~/qrm/v3/purchase-orders(.*)

    int-pos-legacy:
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
        limit-org:
          enabled: true
          plugin: rate-limiting
          config:
            limit_by: header
            header_name: x-apikey-orgid
            minute: 30
            policy: redis
            redis_host: redis-infra-shared-caches.external-service.svc.cluster.local
            redis_database: 2
      route:
        methods:
          - POST
          - PUT
          - GET
          - DELETE
        strip_path: false
      rewrite:
        uri: /purchase-orders/v3$(uri_captures[1]) # Sight-be internal URL
      paths:
        - /~/api/v3/purchase-orders(.*)
      hosts:
        - domains:
            - sight.pre.inspectorio.com
            - integration-api.pre.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

    int-poa: # platform vision: integration.pre.inspectorio.com
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
      route:
        methods:
          - POST
          - DELETE
          - PUT
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v1/purchase-order-associations$(uri_captures[1])
      paths:
        - /~/qrm/v1/purchase-order-associations(.*)

    int-poa-legacy: # legacy domain: sight.pre.inspectorio.com
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
      route:
        methods:
          - POST
          - DELETE
          - PUT
          - PATCH
          - GET
        strip_path: false
        regex_priority: 110
      rewrite:
        uri: /integration/v1/purchase-order-associations$(uri_captures[1])
      paths:
        - /~/api/v1/purchase-order-associations(.*)
      hosts:
        - domains:
            - sight.pre.inspectorio.com
            - integration-api.pre.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

    int-barcode: # platform vision: integration.pre.inspectorio.com
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
      route:
        methods:
          - POST
          - DELETE
          - PUT
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v1/barcode$(uri_captures[1])
      paths:
        - /~/qrm/v1/barcode(.*)

    int-barcode-legacy: # legacy domain: sight.pre.inspectorio.com
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
      route:
        methods:
          - POST
          - DELETE
          - PUT
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v1/barcode$(uri_captures[1])
      paths:
        - /~/api/v1/barcode(.*)
      hosts:
        - domains:
            - sight.pre.inspectorio.com
            - integration-api.pre.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

    labtest: # platform vision: integration.pre.inspectorio.com
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
      route:
        methods:
          - POST
          - DELETE
          - PUT
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v1/lab-tests$(uri_captures[1])
      paths:
        - /~/qrm/v1/lab-tests(.*)

    labtest-legacy: # legacy domain: sight.pre.inspectorio.com
      enabled: true
      appservice: integration-api
      ingressClass: integration
      customPlugins:
        integration-auth:
          enabled: true
          plugin: apikey-auth
          config:
            product: "sight"
      route:
        methods:
          - POST
          - DELETE
          - PUT
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /inspectorio/lab/integration/v1/lab-tests$(uri_captures[1])
      paths:
        - /~/api/v1/lab-tests(.*)
      hosts:
        - domains:
            - sight.pre.inspectorio.com
            - integration-api.pre.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

    boms-api:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
        strip_path: false
      paths:
        - "/inspectorio/boms"

  infra:
    services:
      kafkacluster:
        main:
          topics:
            inspection_action_topic:
              config: # All options https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#message-format-version
                retention.ms: 604800000 # 7 Days
            po_action_topic:
              config:
                retention.ms: 604800000 # 7
            integration-logs: {}
            assets-category-updates:
              config:
                retention.ms: 604800000 # 7 Days
            assets-category-updates-dead-letter-queue:
              config:
                retention.ms: 7776000000 # 90 Days
            eventbus-purchase-order:
              config:
                retention.ms: 7776000000 # 90 Days
          kafkaconnect:
            image: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/kafka/kafka-connect:0.1.0-dev-02d7ff45
            externalCluster: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092 # optional. Address of already existing cluster.
            replicas: 1
            resources:
              requests:
                cpu: "300m"
                memory: 2Gi
              limits:
                cpu: 3
                memory: 2Gi
            jvmOptions:
              -Xmx: 1g
              -Xms: 1g
            externalConfiguration:
              env:
                - name: ES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: eck-datasync-pg-connector-user
                      key: password
                - name: POSTGRES_CDC_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: lst-sight-be
                      key: POSTGRES_CDC_PASSWORD
            readinessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            livenessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            metrics:
              enabled: True
            config:
              config.providers: env
              config.providers.env.class: org.apache.kafka.common.config.provider.EnvVarConfigProvider
              config.storage.replication.factor: 3
              offset.storage.replication.factor: 3
              status.storage.replication.factor: 3
            networkAccessFrom:
              - kafka-ui

            connectors:
              es.integration-logs:
                type: sink
                class: io.confluent.connect.elasticsearch.ElasticsearchSinkConnector
                tasksMax: 3
                config:
                  topics: sight-be.integration-logs
                  connection.url: http://eck-integration-logs-es-http.eck-integration.svc:9200
                  key.converter: org.apache.kafka.connect.json.JsonConverter
                  key.converter.schemas.enable: false
                  key.ignore: true
                  schema.ignore: true
                  value.converter: org.apache.kafka.connect.json.JsonConverter
                  value.converter.schemas.enable: false
                  flush.synchronously: true
                  transforms: AddPrefix,AddSuffix
                  transforms.AddPrefix.type: org.apache.kafka.connect.transforms.RegexRouter
                  transforms.AddPrefix.regex: .*
                  transforms.AddPrefix.replacement: integration-logs
                  transforms.AddSuffix.type: org.apache.kafka.connect.transforms.TimestampRouter
                  transforms.AddSuffix.timestamp.format: yyyy.MM.dd
                  transforms.AddSuffix.topic.format: ${topic}-${timestamp}

              es.datasync-pg-accounts-org:
                type: sink
                class: io.confluent.connect.elasticsearch.ElasticsearchSinkConnector
                tasksMax: 3
                config:
                  topics: kafka-connect-cdc-main.pg.sight-be.public.accounts_org
                  connection.url: http://eck-datasync-pg-es-http.eck-datasync.svc:9200
                  connection.username: connector
                  connection.password: ${env:ES_PASSWORD}
                  key.ignore: false
                  schema.ignore: true
                  write.method: insert
                  behavior.on.null.values: delete
                  flush.synchronously: true
                  transforms: route,extractKey,ReplaceField
                  transforms.route.regex: kafka-connect-cdc-main.pg.(.*)
                  transforms.route.replacement: $1
                  transforms.route.type: org.apache.kafka.connect.transforms.RegexRouter
                  transforms.extractKey.type: org.apache.kafka.connect.transforms.ExtractField$Key
                  transforms.extractKey.field: id
                  transforms.ReplaceField.type: org.apache.kafka.connect.transforms.ReplaceField$Value
                  transforms.ReplaceField.exclude: "location"

              outbox-connector:
                type: source
                topicsConnectorNameOverride: pg.sight-be
                class: io.debezium.connector.postgresql.PostgresConnector
                tasksMax: 1
                topicsconfig:
                  retention.bytes: 7516192768
                config:
                  tombstones.on.delete: "false"
                  plugin.name: pgoutput
                  publication.autocreate.mode: filtered
                  heartbeat.action.query: "INSERT INTO debezium.heartbeat (id, ts) VALUES (1, NOW()) ON CONFLICT(id) DO UPDATE SET ts=EXCLUDED.ts;"
                  heartbeat.interval.ms: 30000
                  signal.data.collection: "debezium.signal"
                  database.hostname: pg-sight-be-main.external-service.svc.cluster.local
                  database.port: 5432
                  database.user: "sight-be_cdc"
                  database.password: ${env:POSTGRES_CDC_PASSWORD}
                  database.dbname: "sight-be"
                  publication.name: dbz_publication
                  table.include.list: "public.event_bus_outbox"
                  snapshot.mode: never
                  transforms: "outbox,unwrap"
                  transforms.unwrap.type: io.debezium.transforms.ExtractNewRecordState
                  transforms.unwrap.add.fields: "payload"
                  transforms.outbox.type: io.debezium.transforms.outbox.EventRouter
                  transforms.outbox.table.expand.json.payload: "true"
                  transforms.outbox.route.topic.replacement: "sight-be.eventbus-${routedByValue}"
                  transforms.outbox.table.fields.additional.placement: "event_id:header,source:header,correlation_id:header,ts:header,aggregatetype:header:entity_type,schema_version:header"
                  transforms.outbox.content.type: "application/json"
                  value.converter: org.apache.kafka.connect.json.JsonConverter
                  value.converter.schemas.enable: "false"

  jobs:
    migration:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background
    publish-event-bus-schemas:
      enabled: true
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background

  alertrules:
    interval: 1m
    rules:
      CeleryTaskHighFailRate:
        description: More than 5% tasks failed for the task {{ $labels.name }} the past 10m.
        runbook_url: https://inspectoriodocs.atlassian.net/wiki/x/tICj3
        summary: Celery high task fail rate {{ $labels.group }}.
        severity:
          warning:
            expr: &celery-task-high-fail-rate |-
              (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="sight-be",name!~"None",queue_name!~"None"}[10m])) / (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="sight-be",name!~"None",queue_name!~"None"}[10m]))+sum by (group, queue_name, name) (increase(celery_task_succeeded_total{group="sight-be",name!~"None",queue_name!~"None"}[10m])))*100) > 5
            for: 10m
          error:
            expr: *celery-task-high-fail-rate
            for: 25m
          critical:
            expr: *celery-task-high-fail-rate
            for: 40m
            labels:
              class: saas-outage
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
      - fms:fms
      - analytic3:analytic3
      - eck-sight:eck-sight-monitoring
      - report-html:report-html
      - kafka-schema-registry:kafka-schema-registry
      - hermes:hermes-be
      - factory-risk-be:factory-risk-be
      - master-data:master-data
      - passport:passport-be
      - translation:translation
      - defect-recommend-be:defect-recommend-be
      - product-risk:product-risk-be
      - eck-datasync:eck-datasync-pg
      - car:car
      - notimanager:notimanager
      - kong:ingress-integration-cp
      - document-validator:document-validator
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
      - ports:
        - port: 9092
          protocol: TCP
        to:
        - namespaceSelector:
            matchLabels:
              name: kafka
          podSelector:
            matchLabels:
              app.kubernetes.io/instance: kafka-main
              app.kubernetes.io/name: kafka
      - ports:
        - port: 5432
          protocol: TCP
        to:
        - namespaceSelector:
            matchLabels:
              name: pgbouncer
          podSelector:
            matchLabels:
              app.kubernetes.io/instance: pgbouncer-sight-be-main
              app.kubernetes.io/name: pgbouncer
    ingress:
      apps:
      - notimanager:notimanager
      - analytic3:analytic3
      - mobileresponder:mobileresponder
      - integration-api:integration-api
      - report-html:report-html
      - permission-dashboard:permission-dashboard
      - passport:passport-be
      - rs:rs-backend
      - sms:sms
      - car:car
      - airflow:airflow
      - tracking:tracking

celery-advanced-exporter:
  enabled: true
  environment:
    - name: CE_BROKER_URL
      value: redis://redis-sight-queues.external-service.svc.cluster.local:6379/3
    - name: CE_RETRY_INTERVAL
      value: 5
