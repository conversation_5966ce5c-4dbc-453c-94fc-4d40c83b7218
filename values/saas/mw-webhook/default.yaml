mw-webhook:
  tolerations:
    - effect: NoSchedule
      key: pool-type
      operator: Equal
      value: "infra"
  nodeSelector:
    infra: "true"

  environment:
    REDIS_DSN: "redis://redis-gitlab.default.svc.cluster.local:6379/1"

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps: []
      custom: 
        - ports:
          - port: 6379
            protocol: TCP
          to:
          - ipBlock:
              cidr: 10.0.0.0/8
    ingress:
      apps: []

  istio:
    ingress:
      default:
        enabled: True
        hosts:
          - mw-webhook.inspectorio.com
        http:
          - timeout: 20s
            match:
              - uri:
                  prefix: /
                ignoreUriCase: true
            route:
              - destination:
                  appservice: main
            retries:
              attempts: 3
              perTryTimeout: 5s
              retryOn: gateway-error,connect-failure,refused-stream,reset
