alerts-integration-webhook:
  generic_chart_name: genapp-v1

  serviceAccount:
    create: true
    enableWorkloadIdentity: true
  
  environment:
    REDIS_HOST: "redis-redis-infra.external-service.svc.cluster.local"
    REDIS_DB: 4

  externalSecretStore:
    alerts-integration-webhook: # name of k8s secret
      source:
        data:
          - secretKey: ANT_PD_INTEGRATION_KEY
            remoteRef:
              key: alerts-integration-webhook-ant-key
          - secretKey: STG_PD_INTEGRATION_KEY
            remoteRef:
              key: alerts-integration-webhook-stg-key
          - secretKey: PRE_PD_INTEGRATION_KEY
            remoteRef:
              key: alerts-integration-webhook-pre-key
          - secretKey: PRD_PD_INTEGRATION_KEY
            remoteRef:
              key: alerts-integration-webhook-prd-key
          - secretKey: ANT_PASSWORD
            remoteRef:
              key: alerts-integration-webhook-ant-password
          - secretKey: STG_PASSWORD
            remoteRef:
              key: alerts-integration-webhook-stg-password
          - secretKey: PRE_PASSWORD
            remoteRef:
              key: alerts-integration-webhook-pre-password
          - secretKey: PRD_PASSWORD
            remoteRef:
              key: alerts-integration-webhook-prd-password

  environmentFrom:
    - secretRef:
        name: alerts-integration-webhook

  probes:
    http:
      alerts-integration-webhook-health-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://alerts-integration-webhook-main.alerts-integration-webhook.svc.cluster.local/api/common/health_check
            labels:
              class: saas-outage
              group: alerts-integration-webhook
              domain_type: private
              domain_site: private

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true
    ingress: # Advanced usage. Full info: https://istio.io/latest/docs/reference/config/networking/virtual-service/
      default:
        enabled: True
        gateways:
          - service
        hosts:
          - alerts-integration-webhook.inspectorio.com
        http:
          - timeout: 20s # set timeout for response
            match:
              - uri:
                  prefix: /api/pagerduty
                ignoreUriCase: true
            route:
              - destination:
                  host: alerts-integration-webhook-main #format is <name of app>-<name of appservice>
                  port:
                    number: 80 # port of appservice
            retries:
              attempts: 3
              perTryTimeout: 2s
              retryOn: gateway-error,connect-failure,refused-stream,reset
