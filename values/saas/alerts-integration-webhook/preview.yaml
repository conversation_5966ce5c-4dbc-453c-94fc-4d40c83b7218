alerts-integration-webhook:
  generic_chart_name: genapp-v1

  serviceAccount:
    create: false
    name: alerts-integration-webhook
    enableWorkloadIdentity: true
  
  environment:
    REDIS_HOST: "redis-infra-shared-caches.external-service.svc.cluster.local"
    REDIS_DB: 4

  istio:
    ingress: # Advanced usage. Full info: https://istio.io/latest/docs/reference/config/networking/virtual-service/
      default:
        enabled: True
        gateways:
          - service
        hosts:
          - ""
        http:
          - timeout: 20s # set timeout for response
            match:
              - uri:
                  prefix: /api
                ignoreUriCase: true
            route:
              - destination:
                  appservice: main 
                  port:
                    number: 80 # port of appservice
            retries:
              attempts: 3
              perTryTimeout: 2s
              retryOn: gateway-error,connect-failure,refused-stream,reset
