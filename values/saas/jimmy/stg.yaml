jimmy:
  kongingress:
    api:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - GET
          - PUT
          - POST
          - DELETE
          - PATCH
        strip_path: true
      paths:
        - "/scim/v2/"

  environment:
    JIMMY_PORT: "8080"
    JIMMY_SCIM_BASE_URL: ""
    HERMES_HOSTNAME: "id.stg.inspectorio.com"
    RISE_HOSTNAME: "rise-api.stg.inspectorio.com"
    RISE_USER_EMAIL: "<EMAIL>"
    RISE_TOKEN_TTL: "86400"
    RISE_ORGANIZATION_ID: "ebc67433-c8d8-4779-a0f1-3f1490e2dde9"
    SIGHT_HOSTNAME: "api.stg.inspectorio.com"
    SIGHT_ORGANIZATION_ID: "555721e3-037c-4dad-8ef9-687136dfd32e"
    SIGHT_USER_EMAIL: "<EMAIL>"
    SIGHT_TOKEN_TTL: "86400"
    ECOSYSTEM_AUTHENTICATION_METHOD: "ins-saml"
    SYSTEM_ADMIN_ENABLED: "true"
    TOKEN_EXPIRY_BUFFER_SECONDS: "600"
  
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps: []
      custom: []
    ingress:
      apps: []
