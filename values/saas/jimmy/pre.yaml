jimmy:
  kongingress:
    api:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - GET
          - PUT
          - POST
          - DELETE
          - PATCH
        strip_path: true
      paths:
        - "/scim/v2/"

  environment:
    JIMMY_PORT: "8080"
    JIMMY_SCIM_BASE_URL: ""
    HERMES_HOSTNAME: "id.pre.inspectorio.com"
    RISE_HOSTNAME: "rise-api.pre.inspectorio.com"
    RISE_USER_EMAIL: "<EMAIL>"
    RISE_TOKEN_TTL: "86400"
    RISE_ORGANIZATION_ID: "c8820e9a-3bc6-4b87-9153-8495789c0625"
    SIGHT_HOSTNAME: "api.pre.inspectorio.com"
    SIGHT_ORGANIZATION_ID: "c62b111e-f843-4e0d-a512-7a447703b385"
    SIGHT_USER_EMAIL: "<EMAIL>"
    SIGHT_TOKEN_TTL: "86400"
    ECOSYSTEM_AUTHENTICATION_METHOD: "ins-saml"
    SYSTEM_ADMIN_ENABLED: "true"
    TOKEN_EXPIRY_BUFFER_SECONDS: "600"
  
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps: []
      custom: []
    ingress:
      apps: []
