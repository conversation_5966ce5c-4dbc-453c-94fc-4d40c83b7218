rise-integration-api:
  appservices:
    main:
      containers:
        main:
          resources:
            limits:
              cpu: 2
              memory: 3Gi
            requests:
              cpu: 0.4
              memory: 512Mi
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload=~'rise-integration-api-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='rise-integration-api-appservice-main'})"
          interval: 15s # call expr each internval
          name: rise_integration_api_pod_rps # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 10
        scalers:
          main:
            enabled: true
            minReplicaCount: 2
            maxReplicaCount: 10
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: 100

  probes:
    http:
      rise-integration-api-public-global-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://rise-integration.inspectorio.com/api/swagger
            labels:
              class: saas-outage
              group: rise-integration-api
              domain_type: public
              domain_site: global

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: []
    egress:
      apps:
      - rs:rs-backend
      - master-data:master-data
      - hermes:hermes-be
      custom:
      - ports:
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
      - ports:
        - port: 27017
          protocol: TCP
        to:
        - ipBlock:
            cidr: 0.0.0.0/0

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    legacy-options: # legacy domain: rise-integration.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - OPTIONS
        strip_path: false
      paths:
          - /
      hosts:
        - domains:
            - rise-integration.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

    default:  # platform vision: integration.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
      rewrite:
        uri: /api$(uri_captures[1])
      paths:
        - /~/rsc(.*)

    login: # platform vision: integration.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - POST
        strip_path: false
      rewrite:
        uri: /api$(uri_captures[1])
      paths:
        - /~/rsc/api(/v1/auth/login$)

    default-legacy: # legacy domain: rise-integration.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        protocols: # support both http and https for proxy from `rise.inspectorio.com/api/` from Istio Ingress
          - https
          - http
      rewrite:
        uri: /api$(uri_captures[1])
      paths:
        - /~/api(.*)
      hosts:
        - domains:
            - rise-integration.inspectorio.com
            - rise.inspectorio.com
            - rise.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls

    legacy-v2-steps: # legacy domain: rise-integration.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        regex_priority: 120
        protocols: # support both http and https for proxy from `rise.inspectorio.com/api/` from Istio Ingress
          - https
          - http
      rewrite:
        uri: /api/v1/reports/$(uri_captures[1])/steps/$(uri_captures[2])
      paths:
        - /~/api/v2/reports/(.*)/steps/(.*)
      hosts:
        - domains:
            - rise-integration.inspectorio.com
            - rise.inspectorio.com
            - rise.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls

    legacy-login: # legacy domain: rise-integration.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - POST
        strip_path: false
        regex_priority: 200
        protocols: # support both http and https for proxy from `rise.inspectorio.com/api/` from Istio Ingress
          - https
          - http
      paths:
        - /~/(api/v1/auth/login$)
      hosts:
        - domains:
            - rise-integration.inspectorio.com
            - rise.inspectorio.com
            - rise.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls

    legacy-swagger:
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - GET
        strip_path: false
        protocols:
          - https
        regex_priority: 110
      paths:
        - /~/api/swagger
      hosts:
        - domains:
            - rise-integration.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true
    ingress:
      default:
        enabled: true
        hosts:
          - rise-integration.inspectorio.com
        http:
          - timeout: 90s
            match:
              - uri:
                  prefix: /api
                ignoreUriCase: true
            route:
              - destination:
                  appservice: main
            retries:
              attempts: 3
              perTryTimeout: 30s
              retryOn: gateway-error,connect-failure,refused-stream,reset
