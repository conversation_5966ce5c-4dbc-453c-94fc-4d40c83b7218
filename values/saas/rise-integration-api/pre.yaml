rise-integration-api:
  appservices:
    main:
      containers:
        main:
          resources:
            limits:
              cpu: 2
              memory: 2.5Gi
            requests:
              cpu: 0.1
              memory: 256Mi
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload=~'rise-integration-api-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='rise-integration-api-appservice-main'})"
          interval: 15s # call expr each internval
          name: rise_integration_api_pod_rps # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 10
        scalers:
          main:
            enabled: true
            minReplicaCount: 2
            maxReplicaCount: 4
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: 100


  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: []
    egress:
      apps:
      - rs:rs-backend
      - master-data:master-data
      - hermes:hermes-be
      custom:
      - ports:
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
      - ports:
        - port: 27017
          protocol: TCP
        to:
        - ipBlock:
            cidr: 0.0.0.0/0

  kongingress:
    legacy-options: # legacy domain: rise-integration.pre.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - OPTIONS
        strip_path: false
      paths:
          - /
      hosts:
        - domains:
            - rise-integration.pre.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

    default:  # platform vision: integration.pre.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
      rewrite:
        uri: /api$(uri_captures[1])
      paths:
        - /~/rsc(.*)

    login: # platform vision: integration.pre.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - POST
        strip_path: false
      paths:
        - /api/v1/auth/login

    default-legacy: # legacy domain: rise-integration.pre.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        protocols: # support both http and https for proxy from `rise.pre.inspectorio.com/api/` from Istio Ingress
          - https
          - http
      rewrite:
        uri: /api$(uri_captures[1])
      paths:
        - /~/api(.*)
      hosts:
        - domains:
            - rise-integration.pre.inspectorio.com
            - rise.pre.inspectorio.com
            - rise-pre.inspectorio-platform.com
            - rise.pre.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls

    legacy-v2-steps: # legacy domain: rise-integration.pre.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        regex_priority: 120
        protocols: # support both http and https for proxy from `rise.pre.inspectorio.com/api/` from Istio Ingress
          - https
          - http
      rewrite:
        uri: /api/v1/reports/$(uri_captures[1])/steps/$(uri_captures[2])
      paths:
        - /~/api/v2/reports/(.*)/steps/(.*)
      hosts:
        - domains:
            - rise-integration.pre.inspectorio.com
            - rise.pre.inspectorio.com
            - rise-pre.inspectorio-platform.com
            - rise.pre.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls

    legacy-login: # legacy domain: rise-integration.pre.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - POST
        strip_path: false
        regex_priority: 200
        protocols: # support both http and https for proxy from `rise.pre.inspectorio.com/api/` from Istio Ingress
          - https
          - http
      paths:
        - /~/(api/v1/auth/login$)
      hosts:
        - domains:
            - rise-integration.pre.inspectorio.com
            - rise.pre.inspectorio.com
            - rise-pre.inspectorio-platform.com
            - rise.pre.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls

    legacy-swagger:
      enabled: true
      appservice: main
      ingressClass: integration
      route:
        methods:
          - GET
        strip_path: false
        protocols: # support both http and https for proxy from `rise.pre.inspectorio.com/api/` from Istio Ingress
          - https
          - http
        regex_priority: 110
      paths:
        - /~/api/swagger
      hosts:
        - domains:
            - rise-integration.pre.inspectorio.com
          tlsSecretName: inspectorio-com-common-tls

  probes:
    http:
      rise-integration-api-public-global-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://rise-integration.pre.inspectorio.com/api/swagger
            labels:
              class: saas-outage
              group: rise-integration-api
              domain_type: public
              domain_site: global

  istio:
    enabled: true
