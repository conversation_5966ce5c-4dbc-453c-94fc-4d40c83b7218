notimanager:
  appservices:
    main:
      replicaCount: 2
      containers:
        main:
          resources:
            requests:
              memory: 400Mi
              cpu: 0.2
            limits:
              memory: 1Gi
              cpu: 1
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='notimanager-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='notimanager-appservice-main'})"
          interval: 10s # call expr each internval
          name: notimanager_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 10
        scalers:
          main:
            enabled: true
            minReplicaCount: 2
            maxReplicaCount: 8
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"

  workers:
    default:
      enabled: true
      containers:
        default:
          resources:
            requests:
              memory: 768Mi
              cpu: 2
            limits:
              memory: 2Gi
              cpu: 4
      nodeSelector:
        saas: "true"
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{exported_namespace='notimanager'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{exported_namespace='notimanager'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{le='+Inf', exported_namespace='notimanager'}[1m]))-sum(rate(celery_task_runtime_bucket{le='2.5', exported_namespace='notimanager'}[1m])))or vector(1)))"
          interval: 10s # call expr each internval
          name: notimanager_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          worker:
            enabled: true
            target: notimanager-worker-default
            minReplicaCount: 1
            maxReplicaCount: 15

  environment:
    AWS_DEFAULT_REGION: us-west-2
    FE_BASE_URL: https://app.pre.inspectorio.com
    SMS_BASE_URL: http://auth-router.default.svc.cluster.local/sms/api/sms
    NMS_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1/{org_id}/notifications
    APPLE_ARN: arn:aws:sns:us-west-2:************:app/APNS_SANDBOX/Apple_Development
    SMS_URL: http://auth-router.default.svc.cluster.local/sms/api/sms
    QUALITY_CONTROL_BE_URL: http://sight-be-main.sight-be.svc.cluster.local
    SMS_USER_NAME: <EMAIL>
    REDIS_URI: redis://redis-sight-queues.external-service.svc.cluster.local:6379/2
    BAIDU_ARN: arn:aws:sns:us-west-2:************:app/BAIDU/InspectorioBaidu
    TOTANGO_KEY: SP-25458-01
    ASSIGNMENTS_BASE_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/ams/v1
    BOOKING_BASE_URL: http://sight-be-main.sight-be.svc.cluster.local/booking/v1
    NOTIMANAGER_BASE_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1
    BAIDU_HOST: api.push.baidu.com
    BAIDU_MODE: 2
    LOGGING_LEVEL: ERROR
    REPORT_TEMPLATE_CHINESE_ID: KQsMtsuYdgdIrSLuIdgtDo71ixW25YEQwSlkLTyQWbA
    APPLE_PHOENIX_ARN: arn:aws:sns:us-west-2:************:app/APNS_SANDBOX/Phoenix_Apple_Development
    REPORT_TEMPLATE_ENGLISH_ID: 97scaswf94SUj0aNrfIh-d7x0KLFT5IlkpLOPO4KO3s
    DATABASE_URL: "postgresql+psycopg2://notimanager:$(POSTGRES_NOTIMANAGER_OWNER_PASSWORD)@pg-notimanager-main.external-service.svc.cluster.local:5432/notimanager?sslmode=require&sslcert=/tmp/dummycertpath"
    WHITELIST_DOMAINS: "inspectorio.com, mailinator.com, inspectorio.mailinator.com, mail-tester.com, lululemon.com, inspectorio.testinator.com, target.com"
    INTEGRATION_SENDER: <EMAIL>
    SENDGRID_WEEKLY_REPORT_TEMPLATE_ID: d-2180a0bcfc34423188504bd9d80cec16
    SENDGRID_FROM_EMAIL: <EMAIL>
    APPLE_NATIVE_ARN: arn:aws:sns:us-west-2:************:app/APNS_SANDBOX/iOS_Native_Development
    SKIP_DELAY_WEBHOOK_ORG_IDS: "315049,318827,318828"
    WHITELIST_EMAILS: "<EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,
      <EMAIL>,"

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: # <namespace>:<app-name>
        - "analytic3:analytic3"
        - "car:car"
        - "factory-risk-be:factory-risk-be"
        - "hermes:hermes-be"
        - "integration-api:integration-api"
        - "mobileresponder:mobileresponder"
        - "passport:passport-be"
        - "permission-dashboard:permission-dashboard"
        - "product-risk:product-risk-be"
        - "sight-be:sight-be"
        - "sms:sms"
        - "superset:analytics-superset"
        - "tracking:tracking"
    egress:
      apps: # <namespace>:<app-name>
        - "notimanager:notimanager"
        - "sight-be:sight-be"
      custom:
        - ports:
          - port: 9092
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: kafka
            podSelector:
              matchLabels:
                app.kubernetes.io/instance: kafka-main
                app.kubernetes.io/name: kafka
        - to:
          - ipBlock:
              cidr: 10.0.0.0/8
          ports:
          - protocol: TCP
            port: 5432 # postgresql
          - protocol: TCP
            port: 6379 # redis

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: false
      rewrite:
        uri: "/nms/$(uri_captures[1])"
      paths:
        - "/~/inspectorio/nms/(.*)"

    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
      rewrite:
        uri: "/nms/$(uri_captures[1])"
      paths:
        - "/~/inspectorio/nms/(.*)"

    deny:
      enabled: true
      appservice: main
      ingressClass: api
      allow_list: [10.0.0.0/8]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: false
      rewrite:
        uri: "/nms/v1/$(uri_captures[1])/webhooks/schedulers"
      paths:
        - "/~/inspectorio/nms/v1/[0-9]+/webhooks/schedulers"

  alertrules:
    interval: 1m
    rules:
      CeleryTaskHighFailRate:
        description: More than 5% tasks failed for the task {{ $labels.name }} the past 10m.
        runbook_url: https://inspectoriodocs.atlassian.net/wiki/x/tICj3
        summary: Celery high task fail rate {{ $labels.group }}.
        severity:
          warning:
            expr: &celery-task-high-fail-rate |-
              (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="notimanager",name!~"None",queue_name!~"None"}[10m])) / (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="notimanager",name!~"None",queue_name!~"None"}[10m]))+sum by (group, queue_name, name) (increase(celery_task_succeeded_total{group="notimanager",name!~"None",queue_name!~"None"}[10m])))*100) > 15
            for: 10m
          error:
            expr: *celery-task-high-fail-rate
            for: 25m
          critical:
            expr: *celery-task-high-fail-rate
            for: 40m
            labels:
              class: saas-outage

  datadog:
    enabled: auto
    options:
      - name: DD_LOGS_INJECTION
        value: "false"
      - name: DD_TAGS
        value: "app_role:$(ROLE),app_component:$(COMPONENT)"

celery-advanced-exporter:
  enabled: true
