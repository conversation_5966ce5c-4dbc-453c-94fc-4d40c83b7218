rs-backend:
  appservices:
    main:
      replicaCount: 2
      enabled: true
      containers:
        main: #container name within a pod
          resources:
            limits:
              cpu: 1
              memory: 2Gi
            requests:
              cpu: 200m
              memory: 448Mi
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload=~'^.*rs-backend-appservice-main.*$'}) / sum(kube_deployment_status_replicas{deployment='rs-backend-appservice-main'})"
          interval: 15s # call expr each internval
          name: rs_backend_per_pod_rps # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 15
        scalers:
          main:
            enabled: true
            minReplicaCount: 2
            maxReplicaCount: 4
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
    bull-exporter:
      replicaCount: 1
      enabled: true
      service:
        type: ClusterIP
        port: 80
        targetPort: main
        enabled: true
        protocol: TCP
      monitoring:
        serviceMonitor:
          enabled: true
          endpoints:
            - port: bull-exporter
              path: /metrics
              interval: 60s
      containers:
        main:
          image:
            repository: uphabit/bull_exporter
            tag: latest
            pullPolicy: IfNotPresent
          podSecurityContext:
            runAsGroup: 65534
            runAsUser: 65534
            runAsNonRoot: true
            privileged: false
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - all
          resources:
            requests:
              cpu: 20m
              memory: 48Mi
            limits:
              cpu: 200m
              memory: 96Mi
          ports:
            - name: main
              containerPort: 9538
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /healthz
              port: main
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /healthz
              port: main
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 5
    bull-queue:
      replicaCount: 1
      enabled: true
      service:
        type: ClusterIP
        port: 80
        targetPort: main
        enabled: true
        protocol: TCP
      containers:
        main:
          environmentFromSecretStore:
            - app
          ports:
            - name: main
              containerPort: 3000
              protocol: TCP
          resources:
            limits:
              cpu: 2
              memory: 2Gi
            requests:
              cpu: 100m
              memory: 128Mi
          livenessProbe:
            httpGet:
              path: /health_check
              port: 3000
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /health_check
              port: 3000
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 10

  environment:
    REQUEST_SIZE_LIMIT: "2mb"
    REDIS_URI: "redis://redis-redis-rs-preprod.external-service.svc.cluster.local:6379/0"
    EXPORTER_REDIS_URL: "redis://redis-redis-rs-preprod.external-service.svc.cluster.local:6379/0"
    EXPORTER_PREFIX: RISE
    EXPORTER_QUEUES: GENERATE_PDF PROCESS_EXTERNAL_REPORT CASE_EXPIRATION ASSESSMENT_GENERATOR
    POSTGRES_HOST: "pg-rs-backend-main.external-service.svc.cluster.local"
    POSTGRES_USER: "rs-backend"
    POSTGRES_DB: "rs-backend"
    PORT: "3000"
    DOCUSIGN_ENV: "demo"
    DOCUSIGN_ACCOUNT_ID: "5760106"
    DOCUSIGN_OAUTH_BASE_URL: "account-d.docusign.com"
    DOCUSIGN_PATH_PRIVATE_KEY: "/app/private-key/docusign_private.txt"
    APP_ENDPOINT: "https://rise.pre.inspectorio.com"
    POSTGRES_PORT: "5432"
    SMS_BASE_URL: "http://auth-router.default.svc.cluster.local/sms/api/sms"
    SMS_SYS_ADMIN_EMAIL: "<EMAIL>"
    MAX_FILE_UPLOAD_SIZE: "********"
    DOCUSIGN_URL: "https://demo.docusign.net/restapi"
    DEDUPE_URL: "http://dedupe-api-main.dedupe-api.svc.cluster.local"
    EMAIL_ENDPOINT: "https://api.sendgrid.com/v3"
    EMAIL_SENDER: "<EMAIL>"
    EMAIL_SENDERNAME: "Inspectorio Rise"
    EMAIL_TEMPLATES_ASSESSMENT: "d-83980c351a7d4ba2b6175fffe6458c64"
    EMAIL_TEMPLATES_USER_INVITATION: "d-8c6eb37a02e34c54b280f111f3ce1449"
    EMAIL_TEMPLATES_USER_INVITATION_REMINDER: "d-edc8b0f75b5c4c7fb4beac84e6903d82"
    EMAIL_TEMPLATES_USER_SIGNING_REMINDER: "d-c6bbf9b685fd4bc5b80a80f77efe4215"
    EMAIL_TEMPLATES_PENDING_ASSESSMENTS_REMINDER: "d-60dd1183d82f48e1a6671fc6e8c8dbf8"
    EMAIL_TEMPLATES_DUE_DATE_REMINDER: "d-4d38d6d5347148089a59313094ae018b"
    GEODB_BASE_URL: "https://wft-geo-db.p.rapidapi.com/v1/geo"
    DOCUSIGN_SUBSCRIPTION_DURATION: "12"
    DOCUSIGN_TEMPLATE_PILOT_ID: "01f60bfe-c84f-4da8-8436-3f18e65a70e5"
    DOCUSIGN_PILOT_DURATION: "12"
    ORIGIN_FOR_SMS_LOGIN: "https://rise.pre.inspectorio.com"
    ONE_ORG_ENABLE: false
    EMAIL_TEMPLATES_ORG_SIGNING_REMINDER: "d-8eb16ae0827b4fc4a5ad4ecbb14f39e7"
    SLCP_VERSION: "v1.3"
    SLCP_BASE_URL: "https://stage-gateway.slconvergence.org/api/slcp"
    HIGG_REPORTS_ASSESSMENT_ID: "81987496-dc84-4070-9909-45a63eed101c"
    SLCP_REPORTS_ASSESSMENT_ID: "ffc5714f-81af-49dd-8b98-5a4b04babc82"
    EMAIL_TEMPLATES_CAPA_NOTIFICATION: "d-4ec20273e3224c09807b2ebfd2438c60"
    EMAIL_TEMPLATES_CASE_STATUS: "d-fb0a3d8e8ef849958e78e2e369e50b63"
    EMAIL_TEMPLATES_NON_COMPLIANT: "d-9b783389fa8a4f0c90c5ed700aa19e9a"
    EMAIL_TEMPLATES_ORGANIZATION_INVITATION: "d-2aeee2a1de55406ebe1a6425aba2744e"
    EMAIL_TEMPLATES_EDIT_ASSESSMENT_FOR_OLD_EXECUTOR: "d-ec41197272db4d3e84ebdb2601869739"
    EMAIL_TEMPLATES_DISCLOSURE: "d-d626eea29ebb4892a77bc779844fb27b"
    EMAIL_TEMPLATES_OFFLINE_SYNC_ISSUE: "d-443c8aa5f8c04ad5941c296f40e12477"
    PRODUCT_CONTEXT: "rise"
    EMAIL_TEMPLATES_SEND_ESCALATION: "d-07baf34ec188444bbfccc3e04d8e9615"
    EMAIL_TEMPLATES_GENERATE_PDF_ISSUE: "d-6aa3b9be0c0042879766900a75862950"
    TERMS_AND_CONDITIONS_ID_EN: "44623482"
    TERMS_AND_CONDITIONS_ID_ES: "61743760"
    TERMS_CAMPAIGN_NAMES: "Covid-19"
    NODE_OPTIONS: "--max_old_space_size=4096"
    ADMIN_EMAIL_LIST: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
    ONE_ORG_BASE_URL: ""
    QAFA_STANDARDS_WITH_NOTIFICATIONS: "5c8866eb-6474-4207-982d-a23d72df977d,05b059f4-6320-424e-ba80-9020fb4465e6,06b65b7c-92ca-403a-a8a4-4ddd6ce05321"
    SSO_SERVICE_WHITE_LIST: "hermes"
    GCP_BUCKET_NAME: "rs-upload-pre"
    STORAGE_PROVIDER: "gcp"
    RISE_INTEGRATION_BASE_URL: "https://rise-integration.pre.inspectorio.com/api/v1"
    ENABLE_AKAMAI_EDGE_AUTH: "true"
    AKAMAI_BUCKET_URL: "https://rise-files.pre.inspectorio.com"
    RISE_TRANSLATION_BASE_URL: "http://translation-main.translation.svc.cluster.local"
    HERMES_BASE_URL: "http://hermes-be-main.hermes.svc.cluster.local"
    MASTER_DATA_BASE_URL: "http://master-data-main.master-data.svc.cluster.local"
    MASTER_DATA_INTERNAL_ENDPOINT: "http://master-data-main.master-data.svc.cluster.local/internal-api/meta-data"
    EMAIL_TEMPLATES_ASSESSMENT_V2: "d-a9d857375cc14884a0510d9ade8f0d87"
    EMAIL_TEMPLATES_BULK_ASSESSMENT_NOTIFICATION: "d-41d8b7c185a547d08f5c5825e31d6e02"
    EMAIL_TEMPLATES_CAPA_NOTIFICATION_V2: "d-ec1fe0c982be4a9dad2ceb0dc0ce2123"
    POSTGRES_MIN_POOL: "5"
    POSTGRES_MAX_POOL: "20"
    ACQUIRE_TIMEOUT_MILLIS: "30000"
    REAP_INTERVAL_MILLIS: "2000"
    PASSPORT_BASE_URL: "http://passport-be-main.passport.svc.cluster.local"
    THIRDPARTY_REPORTS_HOST: "thirdparty-reports-main.thirdparty-reports.svc.cluster.local"
    LOG_LEVEL: "warn"
    SAC_ABBR_NAME: "SAC"
    SSO_BASE_URL: "https://id.pre.inspectorio.com/v1/sso"
    DOCUMENT_VALIDATOR_BASE_URL: "http://document-validator-main.document-validator.svc.cluster.local"
    BULL_BOARD_BASE_PATH: /queues
    INTEGRATION_GATEWAY_ADMIN_URL: "http://kong-gateway-ingress-integration-cp-admin.kong.svc.cluster.local:8001"
    EMAIL_TEMPLATES_UPDATE_SCHEDULING: "d-178fec0f39ee45e4b162ae04b28275c0"
    ORG_RESOLVER_BASE_URL: "http://org-resolver-main.org-resolver.svc.cluster.local"
    SIGHT_BASE_URL: "http://sight-be-main.sight-be.svc.cluster.local"
    DATALAKE_BUCKET_NAME: "inspectorio-ds-datalake-preprod"
    EMAIL_SENDER_QUEUE_CONCURRENCY: "5"

  environmentFromSecrets:
    ES_DATASYNC_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: eck-datasync-pg-rs-backend-user
          key: password

  externalSecretStore:
    eck-datasync-pg-connector-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-connector
              conversionStrategy: Default
              decodingStrategy: None

    eck-datasync-pg-rs-backend-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-rs-backend
              conversionStrategy: Default
              decodingStrategy: None

  infra:
    services:
      kafkacluster:
        main:
          kafkaconnect:
            image: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/kafka/sink-kafka-connect:1.0.0-dev-df60dbc0
            externalCluster: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092 # optional. Address of already existing cluster.
            replicas: 1
            resources:
              requests:
                cpu: "300m"
                memory: 2Gi
              limits:
                cpu: 3
                memory: 2Gi
            jvmOptions:
              -Xmx: 1g
              -Xms: 1g
            externalConfiguration:
              env:
                - name: ES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: eck-datasync-pg-connector-user
                      key: password
            readinessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            livenessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            metrics:
              enabled: True
            config:
              config.providers: env
              config.providers.env.class: org.apache.kafka.common.config.provider.EnvVarConfigProvider
              config.storage.replication.factor: 3
              offset.storage.replication.factor: 3
              status.storage.replication.factor: 3
            networkAccessFrom:
              - kafka-ui

            connectors:
              es.datasync-pg-organizations:
                type: sink
                class: io.confluent.connect.elasticsearch.ElasticsearchSinkConnector
                tasksMax: 3
                config:
                  topics: kafka-connect-cdc-main.pg.rs-backend.public.organizations
                  connection.url: http://eck-datasync-pg-es-http.eck-datasync.svc:9200
                  connection.username: connector
                  connection.password: ${env:ES_PASSWORD}
                  key.ignore: false
                  schema.ignore: true
                  write.method: insert
                  behavior.on.null.values: delete
                  flush.synchronously: true
                  transforms: route,extractKey,ReplaceField
                  transforms.route.regex: kafka-connect-cdc-main.pg.(.*)
                  transforms.route.replacement: $1
                  transforms.route.type: org.apache.kafka.connect.transforms.RegexRouter
                  transforms.extractKey.type: org.apache.kafka.connect.transforms.ExtractField$Key
                  transforms.extractKey.field: id
                  transforms.ReplaceField.type: org.apache.kafka.connect.transforms.ReplaceField$Value
                  transforms.ReplaceField.exclude: >-
                    social_networks,
                    meta

  datadog:
    enabled: auto
    options:
      #https://docs.datadoghq.com/tracing/trace_collection/library_config/python/#pagetitle
      - name: DD_LOGS_INJECTION
        value: "false"
      - name: DD_TRACE_RATE_LIMIT
        value: 10
      - name: DD_TRACE_SAMPLE_RATE
        value: 1.0

  probes:
    http:
      rs-backend-private-health-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://rs-backend-main.rs.svc.cluster.local/health_check
            labels:
              class: saas-outage
              group: rs-backend
              domain_type: private
              domain_site: private
      rs-backend-public-global-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://rise-api.pre.inspectorio.com/status
            labels:
              class: saas-outage
              group: rs-backend
              domain_type: public
              domain_site: global
      rs-backend-public-china-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://rise-api.pre.inspectorio-platform.com/status
            labels:
              class: saas-outage
              group: rs-backend
              domain_type: public
              domain_site: china
  workers:
    default:
      enabled: true
      containers:
        main:
          resources:
            requests:
              cpu: 50m
              memory: 96Mi
            limits:
              cpu: 200m
              memory: 128Mi
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            ENABLE_PDF_QUEUE: "true"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            WORKER_ROLE: "worker"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-b4943b48fb8c4f3fa275235ec761b2da"
            DOWNLOAD_IMPORT_SCHEDULE: "0 19 * * *"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SLCP_ABBR_NAME: "SLCP"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            START_IMPORT_ON_INIT: true
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-pre-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription-pre"

    pdf-generator:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 500m
            limits:
              memory: 1Gi
              cpu: 2.5
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            ENABLE_PDF_QUEUE: "true"
            WORKER_ROLE: "worker_pdf_generator"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-b4943b48fb8c4f3fa275235ec761b2da"
            DOWNLOAD_IMPORT_SCHEDULE: "0 19 * * *"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SLCP_ABBR_NAME: "SLCP"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            START_IMPORT_ON_INIT: true
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-pre-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription-pre"
            JOB_TIMEOUT_MINUTES: "3"
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

    assessment-generator:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1
          environment:
            WORKER_ROLE: "worker_assessment_generator"
            RSC_BACKEND_URL: http://rs-backend-main.rs.svc.cluster.local
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

    email-sender:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1
          environment:
            WORKER_ROLE: "worker_email_sender"
            RSC_BACKEND_URL: http://rs-backend-main.rs.svc.cluster.local
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

    sync-asm-data:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 2
          env:
            ROLE: "sync_asm_data"

    case-expiration-processor:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 2
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            WORKER_ROLE: "case_expiration_processor"
            ENABLE_PDF_QUEUE: "true"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-b4943b48fb8c4f3fa275235ec761b2da"
            DOWNLOAD_IMPORT_SCHEDULE: "0 19 * * *"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SLCP_ABBR_NAME: "SLCP"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            START_IMPORT_ON_INIT: true
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-pre-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription-pre"

    external-report-processor:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 2
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            WORKER_ROLE: "external_report_processor"
            ENABLE_PDF_QUEUE: "true"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-b4943b48fb8c4f3fa275235ec761b2da"
            DOWNLOAD_IMPORT_SCHEDULE: "0 19 * * *"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SLCP_ABBR_NAME: "SLCP"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            START_IMPORT_ON_INIT: true
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-pre-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription-pre"
            JOB_TIMEOUT_MINUTES: "3"
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

  jobs:
    migration:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true
    ingress:
      default:
        enabled: True
        hosts:
          - "rise-api-pre.inspectorio-platform.com"
          - "rise-api.pre.inspectorio-platform.com"
          - "rise-api.pre.inspectorio.com"
        ratelimit:
          login:
            enabled: true
            condition:
              headers:
                - name: "Path-NoParameters"
                  string_match:
                    exact: /login
                - name: "Traffic-class"
                  string_match:
                    exact: world
            threshold: 10rpm # this one need to match pre-defined threshold
        http:
          - timeout: 60s
            match:
              - uri:
                  prefix: /
                ignoreUriCase: true
            route:
              - destination:
                  appservice: "main"
            headers:
              response:
                add:
                  cache-control: no-transform # Necessary to disable gzip compression for China users
        denyRules:
          deny:
            when:
              - key: request.headers[Path-Insensitive]
                values:
                  - "/data-management/add-subscriptions*"
                  - "/data-management/add-subscriptions/*"
                  - "/data-management/deactivate-subscriptions*"
                  - "/data-management/deactivate-subscriptions/*"
        customHttpRoutes:
          pre:
            - match:
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /swagger-ui/
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /swagger-json/
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /swagger.json
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /internal/graphql
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /api/internal
              route:
                - destination:
                    host: rs-backend-main
                    port:
                      number: 80
            - match:
                - ignoreUriCase: true
                  uri:
                    prefix: /swagger-ui/
                - ignoreUriCase: true
                  uri:
                    prefix: /swagger-json/
                - ignoreUriCase: true
                  uri:
                    prefix: /swagger.json
                - ignoreUriCase: true
                  uri:
                    prefix: /internal/graphql
                - ignoreUriCase: true
                  uri:
                    prefix: /api/internal
              directResponse:
                body:
                  string: >-
                    You need to use authorized network to access this service. Please
                    contact administrator !
                status: 403
  kongingress:
    int-v2: # platform vision: integration.stg.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v2/$(uri_captures[1])
      paths:
        - /~/rsc/v2/(.*)
    int-v2-legacy:
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v2/$(uri_captures[1])
      paths:
        - /~/api/v2/(.*)
      hosts:
        - domains:
            - rise-integration.pre.inspectorio.com
            - rise.pre.inspectorio.com
            - rise-pre.inspectorio-platform.com
            - rise.pre.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls
  alertrules:
    prometheusName: prometheus-main
    interval: 1m
    rules:
      SlowBullJobCompletionP90:
        runbook_url: https://grafana.inspectorio.com/d/d5e5fab1-5a1b-474a-b33d-4d827dd87a3d/bull-queue-prometheus?orgId=1&var-cluster_type=pre&var-prometheus=P37195EA69EE965FF&var-container=rs-backend&from=now-7d&to=now
        summary: rs-backend's job queue {{ $labels.queue }} has 90th percentile more than 5 minutes - {{ $value }} seconds
        severity:
          warning:
            expr: sum(bull_queue_complete_duration{app_kubernetes_io_instance="rs-backend", quantile = "0.9"}) by (queue) / 1000 > 180 < 300
            for: 5m
          error:
            expr: sum(bull_queue_complete_duration{app_kubernetes_io_instance="rs-backend", quantile = "0.9"}) by (queue) / 1000 > 300
            for: 2m
      HighFailuresByQueue:
        runbook_url: https://grafana.inspectorio.com/d/d5e5fab1-5a1b-474a-b33d-4d827dd87a3d/bull-queue-prometheus?orgId=1&var-cluster_type=pre&var-prometheus=P37195EA69EE965FF&var-container=rs-backend&from=now-7d&to=now
        summary: rs-backend's job queue {{ $labels.queue }} has more than 20 failures failures
        severity:
          warning:
            expr: max(bull_queue_failed{app_kubernetes_io_instance="rs-backend"}) by (queue) >= 20
            for: 5m

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
      - org-resolver:org-resolver
      - rs:rs-backend
      - dedupe-api:dedupe-api
      - sight-be:sight-be
      - hermes:hermes-be
      - document-validator:document-validator
      - master-data:master-data
      - passport:passport-be
      - kong:ingress-integration-cp
      - translation:translation
      - thirdparty-reports:thirdparty-reports
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
    ingress:
      apps:
      - sms:sms
      - superset:apache-superset
      - h1way:h1way
      - analytic3:analytic3
      - passport:passport-be
      - hermes:hermes-be
      - superset:analytics-superset
      - rs:rise-integration-api
      - document-validator:document-validator
