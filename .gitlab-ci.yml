include:
  - project: devops/gitops-pipeline-templates
    file: library/.gitversion.yaml
  - project: devops/gitops-pipeline-templates
    file: vars.yaml

image: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/python:3.9-legacy

variables:
  TRIGGER_CHARTS:
    value: "bootstrap"
    description: "Space separated names of charts"
  WHEN:
    value: ""
    description: "One of always|manual|on_success. Optional."
  GITOPS_CD_REPO_CLONE_STRING: https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.inspectorio.com/devops/gitops-cd.git
  CD_GIT_STRATEGY: fetch

#get versions and variables once
determine-version:
  extends: .gitversion_function
  rules:
    - if: $CI_MERGE_REQUEST_IID
    - if: $CI_PIPELINE_SOURCE == "web"

.gitops-pipeline:
  stage: pipeline
  variables:
    CI_LINT_CHECKS: "false"
    SCRIPTS_ROOT: ""
    GitVersion_SemVer: $GitVersion_SemVer #important format for downstream pipelines
    DKR_TAG: $DKR_TAG
    REGISTRY_HOST: $REGISTRY_HOST
    PROJECT_ID: $PROJECT_ID
    DKR_REPOSITORY: $DKR_REPOSITORY
    RELEASE_CHANNEL: $RELEASE_CHANNEL
    HELM_CHART: "charts/${CHART_TYPE}/${CI_JOB_NAME}"
    CHART_TYPE: cluster
    WHEN: $WHEN
    CD_GIT_STRATEGY: fetch
  rules:
    - changes:
        - values/${CHART_TYPE}/${APP_NAME}/**/*
        - charts/${CHART_TYPE}/${APP_NAME}/**/*
        - .gitlab/gitops/gitlab-ci-${APP_NAME}.yml
      if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
    - if: $APP_NAME == $TRIGGER_CHARTS && $CI_PIPELINE_SOURCE == "web"&& $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH

stages:
  - init
  - tests
  - pipeline

helm-template:
  stage: tests
  image: $CD_IMAGE
  variables:
    GIT_STRATEGY: fetch
    ELM_REGISTRY_CONFIG: ~/.docker/config.json
  rules:
    - changes:
        - values/cluster/*
        - values/bootstrap/*
        - charts/**/*
        - scripts/helm-validator.py
      if: $CI_PIPELINE_SOURCE == 'merge_request_event'
  parallel:
    matrix:
      - ENV: [ant, stg, pre, prd]
  tags:
    - gke-ant
  script:
    - gcloud auth configure-docker asia-docker.pkg.dev
    - python3 scripts/helm-validator.py --charts-dir=charts --values-dir=values --env ${ENV}

git-secrets:
  stage: tests
  image: zricethezav/gitleaks:latest
  variables:
    GIT_STRATEGY: fetch
    GITLEAKS_CONFIG: gitleaks.toml
  script:
    - gitleaks detect --no-git -v --source=. --baseline-path gitleaks-report.json --report-path findings.json
    - |
      if [[ $? != 0 ]]; then
        echo Call script "gitleaks_update_baseline.sh" in the root of repository and commit gitleaks-report.json if there is false-positive error.
        exit 1
      fi
  artifacts:
    when: on_failure
    paths:
      - findings.json
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
#    - if: $CI_PIPELINE_SOURCE == 'push' && $CI_MERGE_REQUEST_IID == null

bootstrap:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-bootstrap.yml
    strategy: depend
  variables:
    CHART_TYPE: bootstrap
    APP_NAME: bootstrap

baseline:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-baseline.yml
    strategy: depend
  variables:
    APP_NAME: baseline

collabora-online:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-collabora-online.yml
    strategy: depend
  variables:
    APP_NAME: collabora-online

redis-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-redis-operator.yml
    strategy: depend
  variables:
    APP_NAME: redis-operator

ot-redis-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-ot-redis-operator.yml
    strategy: depend
  variables:
    APP_NAME: ot-redis-operator

eck-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/**********************.yml
    strategy: depend
  variables:
    APP_NAME: eck-operator

clickhouse-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/*********************operator.yml
    strategy: depend
  variables:
    APP_NAME: clickhouse-operator

clickhouse-ds:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/*********************ds.yml
    strategy: depend
  variables:
    APP_NAME: clickhouse-ds

pgbouncer:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-pgbouncer.yml
    strategy: depend
  variables:
    APP_NAME: pgbouncer

eck-sight:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-eck-sight.yml
    strategy: depend
  variables:
    APP_NAME: eck-sight

eck-integration:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-eck-integration.yml
    strategy: depend
  variables:
    APP_NAME: eck-integration

eck-infra:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-eck-infra.yml
    strategy: depend
  variables:
    APP_NAME: eck-infra

eck-datasync:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-eck-datasync.yml
    strategy: depend
  variables:
    APP_NAME: eck-datasync

kafka-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kafka-operator.yml
    strategy: depend
  variables:
    APP_NAME: kafka-operator

kafka:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kafka.yml
    strategy: depend
  variables:
    APP_NAME: kafka

kafka-schema-registry:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kafka-schema-registry.yml
    strategy: depend
  variables:
    APP_NAME: kafka-schema-registry

kafka-connect-cdc:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kafka-connect-cdc.yml
    strategy: depend
  variables:
    APP_NAME: kafka-connect-cdc

keptn:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-keptn.yml
    strategy: depend
  variables:
    APP_NAME: keptn

certificates:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-certificates.yml
    strategy: depend
  variables:
    APP_NAME: certificates

grafana:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-grafana.yml
    strategy: depend
  variables:
    APP_NAME: grafana

oncall:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-oncall.yml
    strategy: depend
  variables:
    APP_NAME: oncall

dependency-track:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-dependency-track.yml
    strategy: depend
  variables:
    APP_NAME: dependency-track

influxdb2:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-influxdb2.yml
    strategy: depend
  variables:
    APP_NAME: influxdb2

datadog:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-datadog.yml
    strategy: depend
  variables:
    APP_NAME: datadog

datadog-gateway:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-datadog-gateway.yml
    strategy: depend
  variables:
    APP_NAME: datadog-gateway

spark-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-spark-operator.yml
    strategy: depend
  variables:
    APP_NAME: spark-operator

spark-hs:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-spark-hs.yml
    strategy: depend
  variables:
    APP_NAME: spark-hs

gitlab-runner:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-gitlab-runner.yml
    strategy: depend
  variables:
    APP_NAME: gitlab-runner

kong:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kong.yml
    strategy: depend
  variables:
    APP_NAME: kong

kong-auth-router:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kong-auth-router.yml
    strategy: depend
  variables:
    APP_NAME: kong-auth-router

pomerium:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-pomerium.yml
    strategy: depend
  variables:
    APP_NAME: pomerium

postgres-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-postgres-operator.yml
    strategy: depend
  variables:
    APP_NAME: postgres-operator

pomerium-proxy:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-pomerium-proxy.yml
    strategy: depend
  variables:
    APP_NAME: pomerium-proxy

external-secrets:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-external-secrets.yml
    strategy: depend
  variables:
    APP_NAME: external-secrets

sftpgo:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-sftpgo.yml
    strategy: depend
  variables:
    APP_NAME: sftpgo

monitoring:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-monitoring.yml
    strategy: depend
  variables:
    APP_NAME: monitoring

alerting:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-alerting.yml
    strategy: depend
  variables:
    APP_NAME: alerting

ingress-nginx:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-ingress-nginx.yml
    strategy: depend
  variables:
    APP_NAME: ingress-nginx

sonarqube:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-sonarqube.yml
    strategy: depend
  variables:
    APP_NAME: sonarqube

kafka-ui:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kafka-ui.yml
    strategy: depend
  variables:
    APP_NAME: kafka-ui

redis-ui:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-redis-ui.yml
    strategy: depend
  variables:
    APP_NAME: redis-ui

victoria-metrics:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-victoria-metrics.yml
    strategy: depend
  variables:
    APP_NAME: victoria-metrics

fluentd:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-fluentd.yml
    strategy: depend
  variables:
    APP_NAME: fluentd

istio:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-istio.yml
    strategy: depend
  variables:
    APP_NAME: istio

gcs-files-upload:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-gcs-files-upload.yml
    strategy: depend
  variables:
    APP_NAME: gcs-files-upload

external-service-proxy:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-external-service-proxy.yml
    strategy: depend
  variables:
    APP_NAME: external-service-proxy

patch-operator:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-patch-operator.yml
    strategy: depend
  variables:
    APP_NAME: patch-operator

apicurio:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-apicurio.yml
    strategy: depend
  variables:
    APP_NAME: apicurio

keda:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-keda.yml
    strategy: depend
  variables:
    APP_NAME: keda

kubecost:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-kubecost.yml
    strategy: depend
  variables:
    APP_NAME: kubecost

cloudflare-tunnel:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-cloudflare-tunnel.yml
    strategy: depend
  variables:
    APP_NAME: cloudflare-tunnel

gatekeeper:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-gatekeeper.yml
    strategy: depend
  variables:
    APP_NAME: gatekeeper

values-diff:
  stage: tests
  image: $CD_IMAGE
  variables:
    GIT_STRATEGY: pull
  script:
    - git fetch origin $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - git diff --name-only origin/$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME origin/$CI_MERGE_REQUEST_TARGET_BRANCH_NAME | grep "values/saas/" > changes.txt || true
    - cat changes.txt
    - pip install pyyaml
    - argocd login $ARGOCD_SERVER_URL --insecure --plaintext --username $ARGOCD_USERNAME --password $ARGOCD_PASSWORD
    - python scripts/values_diff.py changes.txt
  rules:
    - changes:
        - values/**/*
        - scripts/values_diff.py
      if: $CI_MERGE_REQUEST_IID

check-service-catalog:
  stage: tests
  image: $CD_IMAGE
  variables:
    GIT_STRATEGY: pull
  script:
    - git fetch origin $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - git diff --name-only origin/$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME origin/$CI_MERGE_REQUEST_TARGET_BRANCH_NAME | grep -Ei "values/saas/|values/cluster|charts/cluster/" > changes.txt || true
    - pip install pyyaml
    - python scripts/product_org_checks.py changes.txt
  rules:
    - changes:
        - charts/cluster/**/*
        - values/**/*
        - scripts/product_org_checks.py
      if: $CI_MERGE_REQUEST_IID

elastic-ui:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-elastic-ui.yml
    strategy: depend
  variables:
    APP_NAME: elastic-ui

devpi-proxy:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/*********************.yml
    strategy: depend
  variables:
    APP_NAME: devpi-proxy

langtrace:
  extends: .gitops-pipeline
  trigger:
    include:
      - local: .gitlab/gitops/gitlab-ci-langtrace.yml
    strategy: depend
  variables:
    APP_NAME: langtrace
