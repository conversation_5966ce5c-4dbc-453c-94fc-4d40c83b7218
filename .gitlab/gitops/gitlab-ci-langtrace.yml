include:
  - project: "devops/gitops-pipeline-templates"
    file: "generic-pipeline.yaml"

variables:
  CD_GIT_STRATEGY: fetch

langtrace-diff:
  extends: .full-diff
  stage: diff
  variables:
    PROJECT: devops
    ENV: $ENV_VALUE
    VARIANT: $ENV_VALUE

  parallel:
    matrix:
      - ENV_VALUE: [stg]
  tags:
    - gke-ant
  rules:
    - if: $CI_MERGE_REQUEST_IID && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
